import { useState, useEffect, useRef } from "react";

interface UseScrollDirectionReturn {
	scrollDirection: "up" | "down" | null;
	isVisible: boolean;
}

export const useScrollDirection = (
	threshold: number = 10
): UseScrollDirectionReturn => {
	const [scrollDirection, setScrollDirection] = useState<
		"up" | "down" | null
	>(null);
	const [isVisible, setIsVisible] = useState(true);
	const lastScrollY = useRef(0);
	const ticking = useRef(false);

	const updateScrollDirection = () => {
		const scrollY = window.scrollY;
		if (scrollY < threshold) {
			setIsVisible(true);
			setScrollDirection(null);
			lastScrollY.current = scrollY;
			ticking.current = false;
			return;
		}

		const direction = scrollY > lastScrollY.current ? "down" : "up";
		const scrollDelta = Math.abs(scrollY - lastScrollY.current);
		if (direction !== scrollDirection && scrollDelta > threshold) {
			setScrollDirection(direction);
			setIsVisible(direction === "up");
		}

		lastScrollY.current = scrollY;
		ticking.current = false;
	};

	const onScroll = () => {
		if (!ticking.current) {
			requestAnimationFrame(updateScrollDirection);
			ticking.current = true;
		}
	};

	useEffect(() => {
		lastScrollY.current = window.scrollY;
		window.addEventListener("scroll", onScroll, { passive: true });
		return () => {
			window.removeEventListener("scroll", onScroll);
		};
	}, [scrollDirection, threshold]);
	return { scrollDirection, isVisible };
};
